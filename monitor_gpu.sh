#!/bin/bash

# GPU Monitoring Script for Training
# Run this in a separate terminal to monitor GPU usage during training

echo "=== GPU Monitoring for Piper Training ==="
echo "Monitoring RTX 3060 (6GB VRAM)"
echo "Press Ctrl+C to stop monitoring"
echo "=========================================="

while true; do
    clear
    echo "=== GPU Status - $(date) ==="
    nvidia-smi --query-gpu=name,memory.used,memory.total,utilization.gpu,temperature.gpu --format=csv,noheader,nounits
    echo ""
    echo "=== Memory Usage Details ==="
    nvidia-smi --query-gpu=memory.used,memory.free,memory.total --format=csv,noheader,nounits | awk -F', ' '{printf "Used: %s MB, Free: %s MB, Total: %s MB\n", $1, $2, $3}'
    echo ""
    echo "=== GPU Utilization ==="
    nvidia-smi --query-gpu=utilization.gpu,utilization.memory --format=csv,noheader,nounits | awk -F', ' '{printf "GPU: %s%%, Memory: %s%%\n", $1, $2}'
    echo ""
    echo "=== Temperature ==="
    nvidia-smi --query-gpu=temperature.gpu --format=csv,noheader,nounits | awk '{printf "Temperature: %s°C\n", $1}'
    echo ""
    echo "Press Ctrl+C to stop monitoring..."
    sleep 2
done
