{"cells": [{"cell_type": "code", "execution_count": 1, "id": "b7df1c85", "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "params = {\n", "    'dataset': 'tuskbyte/nepali_dataset_modified_for_vits',\n", "    'config': 'default',\n", "    'split': 'train',\n", "    'offset': '0',\n", "    'length': '100',\n", "}\n", "\n", "response = requests.get('https://datasets-server.huggingface.co/rows', params=params)"]}, {"cell_type": "code", "execution_count": null, "id": "d34cfd45", "metadata": {}, "outputs": [], "source": ["return"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}