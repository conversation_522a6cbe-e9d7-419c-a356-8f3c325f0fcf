import os
import hashlib
import librosa
import soundfile as sf
from datasets import load_dataset
import pandas as pd
from tqdm import tqdm

def create_thorsten_format_dataset():
    """Download and format the Nepali dataset to match Thorsten-Voice format"""

    print("Loading Nepali dataset from Hugging Face...")
    # Load the augmented Nepali dataset
    ds = load_dataset("rikeshsilwalekg/augmented-dataset")

    # Create output directory structure
    output_dir = "nepali_dataset_thorsten_format"
    wavs_dir = os.path.join(output_dir, "wavs")
    os.makedirs(wavs_dir, exist_ok=True)

    print(f"Dataset loaded. Processing {len(ds['train'])} samples...")

    # Process the dataset
    metadata_entries = []

    for idx, sample in enumerate(tqdm(ds['train'], desc="Processing audio files")):
        try:
            # Get audio data and text - handle both 'sentence' and 'text' field names
            audio_data = sample['audio']['array']
            sample_rate = sample['audio']['sampling_rate']

            # Try to get text from either 'sentence' or 'text' field
            if 'sentence' in sample:
                text = sample['sentence']
            elif 'text' in sample:
                text = sample['text']
            else:
                print(f"Error processing sample {idx}: No text field found (available keys: {list(sample.keys())})")
                continue

            # Skip if text is empty or None
            if not text or text.strip() == '':
                print(f"Error processing sample {idx}: Empty text")
                continue

            # Create unique filename using hash of text
            text_hash = hashlib.md5(text.encode('utf-8')).hexdigest()
            filename = f"{text_hash}.wav"
            filepath = os.path.join(wavs_dir, filename)

            # Skip if file already exists (avoid duplicates)
            if os.path.exists(filepath):
                metadata_entries.append([text_hash, text, text.lower()])
                continue

            # Resample to 22050 Hz if needed and convert to mono
            if sample_rate != 22050:
                audio_data = librosa.resample(audio_data, orig_sr=sample_rate, target_sr=22050)

            # Ensure mono channel
            if len(audio_data.shape) > 1:
                audio_data = librosa.to_mono(audio_data)

            # Save audio file
            sf.write(filepath, audio_data, 22050)

            # Add metadata entry (filename without extension, original text, normalized text)
            # For now, using the same text for both original and normalized
            metadata_entries.append([text_hash, text, text.lower()])

        except Exception as e:
            print(f"Error processing sample {idx}: {e}")
            continue

    # Create metadata.csv file
    metadata_path = os.path.join(output_dir, "metadata.csv")
    with open(metadata_path, 'w', encoding='utf-8') as f:
        for entry in metadata_entries:
            f.write(f"{entry[0]}|{entry[1]}|{entry[2]}\n")

    print(f"Dataset conversion completed!")
    print(f"- Total samples processed: {len(metadata_entries)}")
    print(f"- Output directory: {output_dir}")
    print(f"- Audio files: {wavs_dir}")
    print(f"- Metadata file: {metadata_path}")
    print(f"- Sample rate: 22050 Hz")
    print(f"- Format: WAV, mono channel")

if __name__ == "__main__":
    create_thorsten_format_dataset()
