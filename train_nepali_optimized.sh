#!/bin/bash

# Nepali TTS Training Script - Optimized for 6GB RTX 3060
# This script uses the large Nepali dataset (125,675 samples) with GPU-optimized settings

echo "=== Nepali TTS Training - GPU Optimized ==="
echo "Dataset: 125,675 samples"
echo "GPU: RTX 3060 (6GB VRAM)"
echo "=========================================="

# Activate virtual environment
source venv/bin/activate

# Step 1: Preprocess the large Nepali dataset
echo "Step 1: Preprocessing dataset..."
python3 -m piper_train.preprocess \
  --input-dir /home/<USER>/Documents/personal/piper/nepali_dataset_thorsten_format/ \
  --output-dir /home/<USER>/Documents/personal/piper/nepali_training/ \
  --language ne \
  --sample-rate 22050 \
  --dataset-format ljspeech \
  --single-speaker

if [ $? -ne 0 ]; then
    echo "Error: Preprocessing failed!"
    exit 1
fi

echo "Preprocessing completed successfully!"

# Step 2: Train with GPU-optimized settings for 6GB RTX 3060
echo "Step 2: Starting training with GPU-optimized settings..."
echo "Settings optimized for 6GB VRAM:"
echo "- Batch size: 4 (reduced from 8)"
echo "- Max phoneme IDs: 300 (prevents long sentences from causing OOM)"
echo "- Learning rate: 0.00005 (conservative for fine-tuning)"
echo "- Max epochs: 50 (prevents overfitting)"
echo "- Validation split: 10% (monitors training quality)"

python3 -m piper_train \
    --dataset-dir /home/<USER>/Documents/personal/piper/nepali_training/ \
    --accelerator 'gpu' \
    --devices 1 \
    --batch-size 4 \
    --validation-split 0.1 \
    --num-test-examples 100 \
    --max_epochs 50 \
    --resume_from_checkpoint /home/<USER>/Documents/personal/piper/epoch=6618-step=187068.ckpt \
    --checkpoint-epochs 5 \
    --precision 32 \
    --quality medium \
    --max-phoneme-ids 300 \
    --learning-rate 0.00005

if [ $? -ne 0 ]; then
    echo "Error: Training failed!"
    exit 1
fi

echo "Training completed successfully!"
echo "Check the nepali_training/lightning_logs/ directory for checkpoints"
